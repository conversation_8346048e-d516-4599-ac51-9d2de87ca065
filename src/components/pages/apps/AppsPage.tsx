'use client';

import { useState, useMemo, useCallback } from 'react';
import { HeaderBar } from '@/components/layout/header/HeaderBar';
import { AppItem } from '@/components/pages/apps/AppItem';
import { CategoryFilter } from '@/components/pages/apps/CategoryFilter';

const appList: AppItem[] = [
  {
    appName: 'ASTs',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Stock management',
  },
  {
    appName: 'Amazon app',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Stock management',
  },
  {
    appName: 'Bakery app',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Workforce',
  },
  {
    appName: 'BI app',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Workforce',
  },
  {
    appName: 'Inventory Manager',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Stock management',
  },
  {
    appName: 'Warehouse Scanner',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Stock management',
  },
  {
    appName: 'Staff Scheduler',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Workforce',
  },
  {
    appName: 'Time Tracker',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Workforce',
  },
  {
    appName: 'Training Portal',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Workforce',
  },
  {
    appName: 'Help Desk',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Customer service',
  },
  {
    appName: 'Live Chat',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Customer service',
  },
  {
    appName: 'Customer Portal',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Customer service',
  },
  {
    appName: 'Feedback Manager',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Customer service',
  },
  {
    appName: 'Sales Dashboard',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Analytics',
  },
  {
    appName: 'Performance Metrics',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Analytics',
  },
  {
    appName: 'Data Insights',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Analytics',
  },
  {
    appName: 'Report Builder',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Analytics',
  },
  {
    appName: 'Task Manager',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Operations',
  },
  {
    appName: 'Workflow Builder',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Operations',
  },
  {
    appName: 'Process Monitor',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: 'Operations',
  },
  // Include apps with missing or empty category for edge-case testing
  {
    appName: 'Legacy App',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
  },
  {
    appName: 'Uncategorized App',
    appIcon: '/icons/appicon.png',
    appLaunchUrl: '#',
    category: '',
  },
];

function generateCategoriesFromApps(apps: AppItem[]): string[] {
  const uniqueCategories = new Set<string>();

  apps.forEach((app) => {
    if (app.category && app.category.trim()) {
      uniqueCategories.add(app.category);
    }
  });

  const sortedCategories = Array.from(uniqueCategories).sort();
  return ['All apps', ...sortedCategories];
}

export function AppsPage() {
  const categories = useMemo(() => generateCategoriesFromApps(appList), []);
  const [selectedCategory, setSelectedCategory] = useState<string>('All apps');

  const handleCategorySelect = useCallback((category: string) => {
    setSelectedCategory(category);
  }, []);

  const filteredApps = useMemo(() => {
    if (selectedCategory === 'All apps') {
      return appList;
    }
    return appList.filter((app) => app.category === selectedCategory);
  }, [selectedCategory]);

  return (
    <>
      <HeaderBar>
        <h1 className="text-center w-full font-semibold text-lg">Apps</h1>
      </HeaderBar>
      <div className="flex-1 overflow-y-auto overflow-x-hidden h-screen font-semibold text-2xl pt-16 pb-20">
        <CategoryFilter
          categories={categories}
          selectedCategory={selectedCategory}
          onCategorySelect={handleCategorySelect}
          className="sticky -top-1 inset-x-0 bg-background-blue z-50 mb-[18px] hht:mb-[18px] tablet:mb-[26px] transition-all duration-200 ease-in-out"
        />
        <div className="flex flex-col items-center gap-y-2 hht:gap-y-2 tablet:gap-y-3 mx-2 hht:mx-3 tablet:mx-4 transition-opacity duration-200 ease-out">
          {filteredApps.map((appItem) => (
            <AppItem key={appItem.appName} appItem={appItem} />
          ))}
        </div>
      </div>
    </>
  );
}
